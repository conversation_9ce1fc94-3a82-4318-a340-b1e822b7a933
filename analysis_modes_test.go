package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"log"
	"net/http/httptest"
	"testing"
)

// TestAnalysisModes 测试三种分析模式
func TestAnalysisModes(t *testing.T) {
	// 模拟图片数据（base64编码）
	mockImageData := "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="

	tests := []struct {
		name         string
		mode         AnalysisMode
		expectedCode int
		description  string
	}{
		{
			name:         "Professional Mode",
			mode:         ProfessionalMode,
			expectedCode: 200,
			description:  "测试专业精准模式：OCR + 分析",
		},
		{
			name:         "Standard Mode",
			mode:         StandardMode,
			expectedCode: 200,
			description:  "测试智能均衡模式：直接多模态分析",
		},
		{
			name:         "Economy Mode",
			mode:         EconomyMode,
			expectedCode: 200,
			description:  "测试经济极速模式：快速多模态分析",
		},
		{
			name:         "Default Mode",
			mode:         "",
			expectedCode: 200,
			description:  "测试默认模式（应该使用标准模式）",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fmt.Printf("\n=== %s ===\n", tt.description)

			// 创建请求体
			requestBody := ChatRequest{
				Content:      mockImageData,
				Text:         "评分标准：1. 答案正确性（50分）2. 解题步骤（30分）3. 书写规范（20分）",
				PromptKey:    "math_analysis",
				AnalysisMode: tt.mode,
				Temperature:  0.3,
			}

			jsonData, err := json.Marshal(requestBody)
			if err != nil {
				t.Fatalf("Failed to marshal request: %v", err)
			}

			// 创建HTTP请求
			req := httptest.NewRequest("POST", "/api/v2/chat/analysis", bytes.NewBuffer(jsonData))
			req.Header.Set("Content-Type", "application/json")

			// 模拟用户认证（在实际测试中需要有效的认证）
			// req.Header.Set("X-Access-Token", "valid_token")

			// 创建响应记录器
			// w := httptest.NewRecorder()

			// 注意：这里只是演示代码结构，实际测试需要完整的环境设置
			fmt.Printf("请求模式: %s\n", string(tt.mode))
			fmt.Printf("请求体: %s\n", string(jsonData))

			// 在实际测试中，这里会调用 AnalysisHandler(w, req)
			// AnalysisHandler(w, req)

			fmt.Printf("预期状态码: %d\n", tt.expectedCode)
		})
	}
}

// ExampleAnalysisRequest 演示如何使用不同的分析模式
func ExampleAnalysisRequest() {
	fmt.Println("=== Analysis API 使用示例 ===")

	// 示例1：专业精准模式
	professionalRequest := ChatRequest{
		Content:      "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/...",
		Text:         "评分标准：数学题需要完整的解题步骤和正确答案",
		PromptKey:    "math_analysis",
		AnalysisMode: ProfessionalMode,
		Temperature:  0.3,
	}

	fmt.Println("专业精准模式请求:")
	printJSON(professionalRequest)

	// 示例2：智能均衡模式
	standardRequest := ChatRequest{
		Content:      "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/...",
		Text:         "请分析这道语文题的答案质量",
		PromptKey:    "chinese_analysis",
		AnalysisMode: StandardMode,
		Temperature:  0.3,
	}

	fmt.Println("\n智能均衡模式请求:")
	printJSON(standardRequest)

	// 示例3：经济极速模式
	economyRequest := ChatRequest{
		Content:      "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/...",
		Text:         "快速检查作业完成情况",
		PromptKey:    "general_analysis",
		AnalysisMode: EconomyMode,
		Temperature:  0.3,
	}

	fmt.Println("\n经济极速模式请求:")
	printJSON(economyRequest)
}

// printJSON 格式化打印JSON
func printJSON(v interface{}) {
	jsonData, err := json.MarshalIndent(v, "", "  ")
	if err != nil {
		log.Printf("Failed to marshal JSON: %v", err)
		return
	}
	fmt.Println(string(jsonData))
}

// TestAnalysisResponse 演示不同模式的响应格式
func TestAnalysisResponse(t *testing.T) {
	fmt.Println("=== Analysis API 响应示例 ===")

	// 专业模式响应示例
	professionalResponse := SimplifiedResponse{
		ID: "chatcmpl-professional-123",
		Analysis: &AnalysisResult{
			StudentAnswer:  "学生手写答案：x = 2, y = 3, 因为 2x + y = 7",
			Score:          85,
			GradingDetails: "答案正确(50/50)，步骤完整(25/30)，书写清晰(10/20)。建议改进书写规范。",
		},
		Balance: 9500,
	}

	fmt.Println("专业精准模式响应:")
	printJSON(professionalResponse)

	// 标准模式响应示例
	standardResponse := SimplifiedResponse{
		ID: "chatcmpl-standard-456",
		Analysis: &AnalysisResult{
			StudentAnswer:  "x = 2, y = 3, 解题过程：2x + y = 7",
			Score:          80,
			GradingDetails: "答案正确，解题思路清晰，得分80分。",
		},
		Balance: 9700,
	}

	fmt.Println("\n智能均衡模式响应:")
	printJSON(standardResponse)

	// 经济模式响应示例
	economyResponse := SimplifiedResponse{
		ID: "chatcmpl-economy-789",
		Analysis: &AnalysisResult{
			StudentAnswer:  "答案基本正确",
			Score:          75,
			GradingDetails: "作业已完成，答案大致正确。",
		},
		Balance: 9900,
	}

	fmt.Println("\n经济极速模式响应:")
	printJSON(economyResponse)
}

// 如果你想运行这些示例，可以取消注释下面的 main 函数
/*
func main() {
	fmt.Println("Analysis API 三种模式演示")

	// 运行请求示例
	ExampleAnalysisRequest()

	fmt.Println("\n" + strings.Repeat("=", 50))

	// 运行响应示例
	ExampleAnalysisResponse()
}
*/
